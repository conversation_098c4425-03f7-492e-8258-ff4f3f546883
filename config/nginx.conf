server {
    # You would want to make a separate file with its own server block for each virtual domain
    # on your server and then include them.
    listen       80;
    #tells <PERSON><PERSON><PERSON> the hostname and the TCP port where it should listen for HTTP connections.
    # listen 80; is equivalent to listen *:80;
    
    server_name  localhost;
    # lets you doname-based virtual hosting

    #charset koi8-r;

    #access_log  logs/host.access.log  main;

    location / {
        #The location setting lets you configure how nginx responds to requests for resources within the server.
        root   html;
        index  index.html index.htm;
    }

    location /api/ {
        rewrite ^/api(.*) $1 break;
        proxy_pass http://api:4567;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    error_page  404              /404.html;
    location = /404.html {
        root   html;
    }

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
