services:
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: api
    volumes:
      - ./api:/myapp
    environment:
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_HOST: ${MYSQL_HOST}
    depends_on:
      - mariadb
    restart: always
    networks:
      - matcha
    #to run when nginx is not running
    ports:
      - "4567:4567"

  # frontend:
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #   container_name: frontend
  #   volumes:
  #     - ./frontend:/app
  #   ports:
  #     - "8080:80"
  #   depends_on:
  #     - rails_app
  #   environment:
  #     - REACT_APP_API_URL=http://rails_app:3000

# Nginx is mute just to optimise in dev
  # nginx:
  #   image: nginx:latest
  #   container_name: nginx
  #   volumes:
  #     - ./config/nginx.conf:/etc/nginx/conf.d/default.conf
  #     - ./frontend/build:/etc/nginx/html
  #   ports:
  #     - "80:80"
  #   depends_on:
  #     - api
  #     # - frontend
  #   restart: always
  #   networks:
  #     - matcha

  mariadb:
    image: mariadb:latest
    command: --bind-address=0.0.0.0
    container_name: mariadb
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
    ports:
      - "3306:3306"
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./config/mariadb:/docker-entrypoint-initdb.d
    networks:
      - matcha
  
networks:
  matcha:
    driver: bridge

volumes:
  mariadb_data:
