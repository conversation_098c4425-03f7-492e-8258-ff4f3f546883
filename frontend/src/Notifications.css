.notifications-page {
  min-height: 100vh;
  background-color: #1a1a1a;
  color: #fff;
}

.notifications-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.notifications-container h1 {
  margin-bottom: 2rem;
  color: #ff6f61;
  text-align: center;
}

.notification-list {
  background-color: #2c2c2c;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.notification-item {
  padding: 1.5rem;
  border-bottom: 1px solid #3a3a3a;
  cursor: pointer;
  transition: background-color 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background-color: #333;
}

.notification-item.unread {
  background-color: rgba(48, 30, 255, 0.1);
}

.notification-item.unread:hover {
  background-color: rgba(48, 30, 255, 0.2);
}

.notification-content {
  flex-grow: 1;
}

.notification-message {
  margin: 0 0 0.5rem;
  font-size: 1rem;
}

.notification-time {
  color: #aaa;
  font-size: 0.8rem;
}

.unread-indicator {
  width: 10px;
  height: 10px;
  background-color: #301eff;
  border-radius: 50%;
  margin-left: 1rem;
}

.no-notifications, .loading {
  text-align: center;
  margin: 3rem 0;
  font-size: 1.2rem;
  color: #aaa;
}