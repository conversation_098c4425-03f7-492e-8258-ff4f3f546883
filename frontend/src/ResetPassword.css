@import url('./App.css');

.reset-password-container {
  background: #2c2c2c;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5);
  text-align: center;
  width: 350px;
  margin: 2rem auto;
  color: #fff;
}

.reset-password-container h1 {
  margin-bottom: 1.5rem;
  color: #ff6f61;
}

.reset-password-form {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.reset-password-form p {
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.reset-password-form input {
  width: 80%;
  padding: 0.8rem;
  margin: 0.5rem 0;
  border: 1px solid #444;
  border-radius: 5px;
  font-size: 1rem;
  background: #444;
  color: #fff;
  text-align: center;
}

.reset-password-form input::placeholder {
  color: #aaa;
}

.reset-password-form button {
  background: #301eff;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 1rem;
  transition: background 0.3s ease, transform 0.2s ease;
}

.reset-password-form button:hover {
  background: #9187ff;
  transform: scale(1.05);
}

.login-link {
  margin-top: 1.5rem;
  font-size: 0.9rem;
}

.login-link button {
  background: none;
  border: none;
  color: #ff6f61;
  cursor: pointer;
  font-size: 0.9rem;
  padding: 0;
  margin-left: 0.3rem;
  text-decoration: underline;
}

.login-link button:hover {
  color: #ff8a7f;
}

.message {
  margin-top: 1rem;
  font-size: 1rem;
  color: #ff6f61;
}